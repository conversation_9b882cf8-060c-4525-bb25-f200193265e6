import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:logging/logging.dart';
import 'dart:io';

class TestHelper {
  static final Logger _logger = Logger('IntegrationTest');
  static bool _initialized = false;
  static late IntegrationTestWidgetsFlutterBinding binding;

  static void initializeLogging() {
    if (_initialized) return;
    
    // Initialize the integration test binding
    binding = IntegrationTestWidgetsFlutterBinding.ensureInitialized();
    
    Logger.root.level = Level.ALL;
    Logger.root.onRecord.listen((record) {
      final timestamp = DateTime.now().toIso8601String();
      final message = '''
╔════════════════════════════════════════════════════════════
║ ${record.level.name}: ${record.message}
║ Time: $timestamp
║ Device Info:
║   Platform: ${Platform.operatingSystem} ${Platform.operatingSystemVersion}
║   Screen size: ${binding.window.physicalSize}
║   Screen density: ${binding.window.devicePixelRatio}
╚════════════════════════════════════════════════════════════''';
      // Use red color for errors, yellow for warnings, and green for info
      final color = record.level == Level.SEVERE ? '\x1B[31m' :
                    record.level == Level.WARNING ? '\x1B[33m' : '\x1B[32m';
      print('$color$message\x1B[0m'); // Reset color at the end
    });
    
    _initialized = true;
    _logger.info('Test environment initialized');
  }

  static Future<void> waitFor(WidgetTester tester, Duration duration) async {
    try {
      await tester.pumpAndSettle(const Duration(milliseconds: 100));
      await Future.delayed(duration);
    } catch (e, stack) {
      _logger.warning('Error while waiting: $e\n$stack');
      // Continue execution even if pumpAndSettle fails
      await Future.delayed(duration);
    }
  }

  static void logTestStart(String testName) {
    _logger.info('Starting test: $testName');
    _logTestContext();
  }

  static void logTestEnd(String testName) {
    _logger.info('Completed test: $testName');
  }

  static void logError(String message, dynamic error, StackTrace stackTrace) {
    _logger.severe('''
ERROR: $message
Error details: $error
Stack trace: $stackTrace
''');
  }

  static void _logTestContext() {
    try {
      final testContext = '''
Test Context:
  Memory: ${binding.defaultBinaryMessenger.toString()}
  Surface size: ${binding.window.physicalSize}
''';
      _logger.info(testContext);
    } catch (e) {
      _logger.warning('Could not log test context: $e');
    }
  }

  // Manual testing helpers
  static void logAction(String action) {
    _logger.info('👤 USER ACTION: $action');
  }

  static void logSystemEvent(String event) {
    _logger.info('🔧 SYSTEM: $event');
  }

  static void logNavigation(String route) {
    _logger.info('🔄 NAVIGATION: $route');
  }

  static void logApiCall(String endpoint, {String? method, String? status}) {
    _logger.info('🌐 API ${method ?? 'CALL'}: $endpoint ${status != null ? '(Status: $status)' : ''}');
  }

  static void logSecurityEvent(String event) {
    _logger.warning('🔒 SECURITY: $event');
  }

  static void logPerformance(String metric, dynamic value) {
    _logger.info('⚡ PERFORMANCE: $metric: $value');
  }

  static void printManualTestInstructions() {
    print('''
\x1B[32m
=================================================================
🔍 Manual Testing Mode Active
=================================================================
You can now interact with the app as a normal user.
All actions will be logged to this console.

Log Types You'll See:
👤 USER ACTION    - Your interactions with the app
🔧 SYSTEM        - System events and state changes
🔄 NAVIGATION    - Screen transitions
🌐 API           - Network calls
🔒 SECURITY      - Security-related events
⚡ PERFORMANCE   - Performance metrics

The app will remain running until you press Ctrl+C to exit.
=================================================================
\x1B[0m
''');
  }

  static String getCurrentEnvironment() {
    return Platform.environment['FLUTTER_TEST_ENV'] ?? 'development';
  }

  static Map<String, dynamic> getDeviceInfo() {
    return {
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
      'locale': Platform.localeName,
      'processors': Platform.numberOfProcessors,
      'screen': {
        'size': binding.window.physicalSize.toString(),
        'density': binding.window.devicePixelRatio,
      }
    };
  }
}
