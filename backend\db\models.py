"""Consolidated database models for TrustChain-Auth."""

import enum
from datetime import datetime
from decimal import Decimal
from sqlalchemy import Column, Integer, String, Float, JSON, DateTime, ForeignKey, Boolean, Enum as SQLAEnum, Numeric, Index
from sqlalchemy.orm import relationship

from db.base_class import Base

# ============================================================================
# ENUMS
# ============================================================================

class UserRole(str, enum.Enum):
    ADMIN = "admin"
    USER = "user"
    DEVICE = "device"

class DeviceType(str, enum.Enum):
    MOBILE = "mobile"
    DESKTOP = "desktop"
    TABLET = "tablet"
    WEB = "web"

class Platform(str, enum.Enum):
    IOS = "ios"
    ANDROID = "android"
    WINDOWS = "windows"
    MACOS = "macos"
    LINUX = "linux"
    WEB = "web"

class EventType(str, enum.Enum):
    LOGIN_ATTEMPT = "login_attempt"
    RISK_ASSESSMENT = "risk_assessment"
    CHALLENGE_TRIGGERED = "challenge_triggered"
    LOGOUT = "logout"
    DEVICE_REGISTERED = "device_registered"

class AccountType(str, enum.Enum):
    """Bank account type enumeration."""
    CHECKING = "checking"
    SAVINGS = "savings"
    CREDIT = "credit"
    INVESTMENT = "investment"

class TransactionType(str, enum.Enum):
    """Transaction type enumeration."""
    DEPOSIT = "deposit"
    WITHDRAWAL = "withdrawal"
    TRANSFER = "transfer"
    PAYMENT = "payment"
    FEE = "fee"
    INTEREST = "interest"

class TransactionStatus(str, enum.Enum):
    """Transaction status enumeration."""
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PROCESSING = "processing"

# ============================================================================
# CORE MODELS
# ============================================================================

class User(Base):
    """User model for authentication and user data."""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    password_hash = Column(String)
    first_name = Column(String)
    last_name = Column(String)
    full_name = Column(String)
    role = Column(SQLAEnum(UserRole), default=UserRole.USER)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    devices = relationship("Device", back_populates="user")
    auth_events = relationship("AuthEvent", back_populates="user")
    security_alerts = relationship("SecurityAlert", back_populates="user")
    behavioral_events = relationship("BehavioralEvent", back_populates="user")
    behavioral_features = relationship("BehavioralFeature", back_populates="user")
    behavioral_profile = relationship("BehavioralProfile", back_populates="user", uselist=False)
    risk_assessments = relationship("RiskAssessment", back_populates="user")
    # Banking relationships
    accounts = relationship("Account", back_populates="user")
    transactions = relationship("Transaction", back_populates="user")

class Device(Base):
    """Device model for managing user devices."""
    __tablename__ = "devices"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_type = Column(SQLAEnum(DeviceType))
    device_id = Column(String, unique=True, index=True)  # Unique device identifier
    device_name = Column(String)  # User-friendly device name
    platform = Column(SQLAEnum(Platform))
    last_used = Column(DateTime, default=datetime.utcnow)
    is_trusted = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    device_info = Column(JSON)  # Additional device information
    
    # Relationships
    user = relationship("User", back_populates="devices")
    auth_events = relationship("AuthEvent", back_populates="device")
    security_alerts = relationship("SecurityAlert", back_populates="device")
    behavioral_events = relationship("BehavioralEvent", back_populates="device")
    behavioral_features = relationship("BehavioralFeature", back_populates="device")
    risk_assessments = relationship("RiskAssessment", back_populates="device")

# ============================================================================
# AUTHENTICATION MODELS
# ============================================================================

class AuthEvent(Base):
    """Model for authentication events."""
    __tablename__ = "auth_events"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), index=True)
    event_type = Column(SQLAEnum(EventType))
    risk_score = Column(Float)
    timestamp = Column(DateTime, default=datetime.utcnow)
    device_info = Column(JSON)
    location = Column(JSON)
    success = Column(Boolean, default=False)
    
    # Relationships
    user = relationship("User", back_populates="auth_events")
    device = relationship("Device", back_populates="auth_events")

class SecurityAlert(Base):
    """Model for security alerts."""
    __tablename__ = "security_alerts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), nullable=True)
    alert_type = Column(String)  # high_risk, panic_gesture, suspicious_activity
    severity = Column(String)  # low, medium, high, critical
    description = Column(String)
    timestamp = Column(DateTime, default=datetime.utcnow)
    device_info = Column(JSON, nullable=True)
    is_resolved = Column(Boolean, default=False)
    resolution_notes = Column(String, nullable=True)
    resolved_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="security_alerts")
    device = relationship("Device", back_populates="security_alerts")

# ============================================================================
# BEHAVIORAL BIOMETRICS MODELS
# ============================================================================

class BehavioralEvent(Base):
    """Behavioral events like typing and touch interactions."""
    __tablename__ = "behavioral_events"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), index=True)
    session_id = Column(String, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    event_type = Column(String)  # 'typing', 'touch', etc.
    data = Column(JSON)  # Event-specific data
    context = Column(JSON)  # Device context, app state, etc.

    # Relationships
    user = relationship("User", back_populates="behavioral_events")
    device = relationship("Device", back_populates="behavioral_events")

class BehavioralFeature(Base):
    """Extracted behavioral features."""
    __tablename__ = "behavioral_features"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), index=True)
    session_id = Column(String, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    feature_type = Column(String)  # 'typing', 'touch', 'combined'
    features = Column(JSON)  # Extracted feature vector
    feature_metadata = Column(JSON)  # Feature extraction metadata

    # Relationships
    user = relationship("User", back_populates="behavioral_features")
    device = relationship("Device", back_populates="behavioral_features")

class BehavioralProfile(Base):
    """User's behavioral profile."""
    __tablename__ = "behavioral_profiles"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)

    # Profile data
    profile_type = Column(String)  # 'baseline', 'current', etc.
    latent_features = Column(JSON)  # Encoded behavioral features
    metrics = Column(JSON)  # Profile quality metrics
    thresholds = Column(JSON)  # Anomaly detection thresholds
    profile_metadata = Column(JSON)  # Training metadata, version info, etc.

    # Relationships
    user = relationship("User", back_populates="behavioral_profile")

class RiskAssessment(Base):
    """Behavioral risk assessments."""
    __tablename__ = "risk_assessments"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), index=True)
    session_id = Column(String, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow)

    # Risk scores
    risk_score = Column(Float)
    anomaly_score = Column(Float)
    confidence = Column(Float)

    # Assessment details
    features_used = Column(JSON)  # List of features used
    model_version = Column(String)  # Model version used
    decision = Column(String)  # 'allow', 'block', 'challenge'
    explanation = Column(JSON)  # Decision explanation
    assessment_metadata = Column(JSON)  # Additional assessment metadata

    # Relationships
    user = relationship("User", back_populates="risk_assessments")
    device = relationship("Device", back_populates="risk_assessments")

# ============================================================================
# BANKING MODELS
# ============================================================================

class Account(Base):
    """Bank account model."""
    __tablename__ = "accounts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    account_number = Column(String, unique=True, index=True)
    account_type = Column(SQLAEnum(AccountType))
    account_name = Column(String)  # User-friendly name
    balance = Column(Numeric(precision=15, scale=2), default=0.00)
    currency = Column(String, default="USD")
    is_active = Column(Boolean, default=True)
    is_primary = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Account metadata
    routing_number = Column(String)
    bank_name = Column(String)
    account_metadata = Column(JSON)  # Additional account information

    # Relationships
    user = relationship("User", back_populates="accounts")
    transactions = relationship("Transaction", back_populates="account")
    transfers_from = relationship("Transfer", foreign_keys="Transfer.from_account_id", back_populates="from_account")
    transfers_to = relationship("Transfer", foreign_keys="Transfer.to_account_id", back_populates="to_account")

class Transaction(Base):
    """Transaction model for all banking transactions."""
    __tablename__ = "transactions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    account_id = Column(Integer, ForeignKey("accounts.id"), index=True)
    transaction_id = Column(String, unique=True, index=True)  # External transaction ID
    transaction_type = Column(SQLAEnum(TransactionType))
    status = Column(SQLAEnum(TransactionStatus), default=TransactionStatus.PENDING)

    # Transaction details
    amount = Column(Numeric(precision=15, scale=2))
    currency = Column(String, default="USD")
    description = Column(String)
    reference_number = Column(String)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    processed_at = Column(DateTime)
    completed_at = Column(DateTime)

    # Transaction metadata
    merchant_info = Column(JSON)  # For payments
    location_info = Column(JSON)  # Transaction location
    device_info = Column(JSON)  # Device used for transaction
    risk_score = Column(Float)  # Behavioral risk score at time of transaction
    transaction_metadata = Column(JSON)  # Additional transaction data

    # Relationships
    user = relationship("User", back_populates="transactions")
    account = relationship("Account", back_populates="transactions")

class Transfer(Base):
    """Transfer model for money transfers between accounts."""
    __tablename__ = "transfers"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    from_account_id = Column(Integer, ForeignKey("accounts.id"), index=True)
    to_account_id = Column(Integer, ForeignKey("accounts.id"), index=True)
    transfer_id = Column(String, unique=True, index=True)

    # Transfer details
    amount = Column(Numeric(precision=15, scale=2))
    currency = Column(String, default="USD")
    description = Column(String)
    status = Column(SQLAEnum(TransactionStatus), default=TransactionStatus.PENDING)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    processed_at = Column(DateTime)
    completed_at = Column(DateTime)

    # Transfer metadata
    transfer_type = Column(String)  # 'internal', 'external', 'wire', etc.
    fees = Column(Numeric(precision=15, scale=2), default=0.00)
    exchange_rate = Column(Float)  # For currency conversions
    risk_score = Column(Float)  # Behavioral risk score
    transfer_metadata = Column(JSON)

    # Relationships
    user = relationship("User")
    from_account = relationship("Account", foreign_keys=[from_account_id], back_populates="transfers_from")
    to_account = relationship("Account", foreign_keys=[to_account_id], back_populates="transfers_to")

# Add indexes for better performance
Index('idx_transactions_user_date', Transaction.user_id, Transaction.created_at)
Index('idx_transactions_account_date', Transaction.account_id, Transaction.created_at)
Index('idx_transfers_user_date', Transfer.user_id, Transfer.created_at)
Index('idx_accounts_user_type', Account.user_id, Account.account_type)
