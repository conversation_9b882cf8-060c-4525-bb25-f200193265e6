"""Banking service for account and transaction management."""

import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, func
from sqlalchemy.orm import selectinload

from db.models import Account, Transaction, Transfer, User, AccountType, TransactionType, TransactionStatus
from schemas.banking import (
    AccountCreate, AccountUpdate, AccountResponse, AccountSummary,
    TransactionCreate, TransactionResponse, TransactionHistory,
    TransferCreate, TransferResponse,
    PaymentCreate, PaymentResponse,
    BalanceResponse, AccountSummaryResponse, DashboardSummary
)
from core.logging_config import logger


class BankingService:
    """Service for banking operations."""

    def __init__(self, db: AsyncSession):
        self.db = db

    # ============================================================================
    # ACCOUNT MANAGEMENT
    # ============================================================================

    async def create_account(self, user_id: int, account_data: AccountCreate) -> AccountResponse:
        """Create a new bank account for a user."""
        try:
            # Generate unique account number
            account_number = self._generate_account_number()
            
            # If this is the first account, make it primary
            existing_accounts = await self.get_user_accounts(user_id)
            is_primary = len(existing_accounts) == 0 or account_data.is_primary

            account = Account(
                user_id=user_id,
                account_number=account_number,
                account_type=account_data.account_type,
                account_name=account_data.account_name,
                balance=account_data.initial_balance or Decimal('0.00'),
                currency=account_data.currency,
                is_primary=is_primary,
                bank_name="TrustChain Bank",
                routing_number="*********"
            )

            self.db.add(account)
            await self.db.commit()
            await self.db.refresh(account)

            logger.info(f"Created account {account_number} for user {user_id}")
            return AccountResponse.from_orm(account)

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating account for user {user_id}: {e}")
            raise

    async def get_user_accounts(self, user_id: int) -> List[AccountSummary]:
        """Get all accounts for a user."""
        try:
            result = await self.db.execute(
                select(Account).where(
                    and_(Account.user_id == user_id, Account.is_active == True)
                ).order_by(Account.is_primary.desc(), Account.created_at)
            )
            accounts = result.scalars().all()
            return [AccountSummary.from_orm(account) for account in accounts]

        except Exception as e:
            logger.error(f"Error getting accounts for user {user_id}: {e}")
            raise

    async def get_account(self, user_id: int, account_id: int) -> Optional[AccountResponse]:
        """Get a specific account for a user."""
        try:
            result = await self.db.execute(
                select(Account).where(
                    and_(
                        Account.id == account_id,
                        Account.user_id == user_id,
                        Account.is_active == True
                    )
                )
            )
            account = result.scalar_one_or_none()
            return AccountResponse.from_orm(account) if account else None

        except Exception as e:
            logger.error(f"Error getting account {account_id} for user {user_id}: {e}")
            raise

    async def update_account(self, user_id: int, account_id: int, account_data: AccountUpdate) -> Optional[AccountResponse]:
        """Update account information."""
        try:
            result = await self.db.execute(
                select(Account).where(
                    and_(
                        Account.id == account_id,
                        Account.user_id == user_id
                    )
                )
            )
            account = result.scalar_one_or_none()
            
            if not account:
                return None

            # Update fields
            if account_data.account_name is not None:
                account.account_name = account_data.account_name
            if account_data.is_primary is not None:
                account.is_primary = account_data.is_primary
            if account_data.is_active is not None:
                account.is_active = account_data.is_active

            account.updated_at = datetime.utcnow()
            
            await self.db.commit()
            await self.db.refresh(account)

            logger.info(f"Updated account {account_id} for user {user_id}")
            return AccountResponse.from_orm(account)

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating account {account_id} for user {user_id}: {e}")
            raise

    # ============================================================================
    # BALANCE MANAGEMENT
    # ============================================================================

    async def get_account_balance(self, user_id: int, account_id: int) -> Optional[BalanceResponse]:
        """Get account balance information."""
        try:
            account = await self.get_account(user_id, account_id)
            if not account:
                return None

            # Calculate pending transactions
            pending_result = await self.db.execute(
                select(func.coalesce(func.sum(Transaction.amount), 0)).where(
                    and_(
                        Transaction.account_id == account_id,
                        Transaction.status == TransactionStatus.PENDING
                    )
                )
            )
            pending_balance = pending_result.scalar() or Decimal('0.00')

            return BalanceResponse(
                account_id=account.id,
                account_name=account.account_name,
                account_type=account.account_type,
                balance=account.balance,
                currency=account.currency,
                available_balance=account.balance - pending_balance,
                pending_balance=pending_balance,
                last_updated=account.updated_at
            )

        except Exception as e:
            logger.error(f"Error getting balance for account {account_id}: {e}")
            raise

    async def update_account_balance(self, account_id: int, amount: Decimal, transaction_type: TransactionType) -> bool:
        """Update account balance based on transaction."""
        try:
            result = await self.db.execute(
                select(Account).where(Account.id == account_id)
            )
            account = result.scalar_one_or_none()
            
            if not account:
                return False

            # Calculate new balance based on transaction type
            if transaction_type in [TransactionType.DEPOSIT, TransactionType.INTEREST]:
                account.balance += amount
            elif transaction_type in [TransactionType.WITHDRAWAL, TransactionType.PAYMENT, TransactionType.FEE]:
                account.balance -= amount
                if account.balance < 0:
                    logger.warning(f"Account {account_id} balance went negative: {account.balance}")

            account.updated_at = datetime.utcnow()
            await self.db.commit()

            logger.info(f"Updated balance for account {account_id}: {account.balance}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating balance for account {account_id}: {e}")
            raise

    # ============================================================================
    # UTILITY METHODS
    # ============================================================================

    def _generate_account_number(self) -> str:
        """Generate a unique account number."""
        # Simple account number generation - in production, use more sophisticated logic
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_suffix = str(uuid.uuid4().int)[:6]
        return f"TC{timestamp}{random_suffix}"

    def _generate_transaction_id(self) -> str:
        """Generate a unique transaction ID."""
        return f"TXN{uuid.uuid4().hex[:12].upper()}"

    def _generate_transfer_id(self) -> str:
        """Generate a unique transfer ID."""
        return f"TRF{uuid.uuid4().hex[:12].upper()}"

    # ============================================================================
    # TRANSACTION MANAGEMENT
    # ============================================================================

    async def create_transaction(self, user_id: int, transaction_data: TransactionCreate) -> TransactionResponse:
        """Create a new transaction."""
        try:
            # Verify account belongs to user
            account = await self.get_account(user_id, transaction_data.account_id)
            if not account:
                raise ValueError("Account not found or does not belong to user")

            transaction_id = self._generate_transaction_id()

            transaction = Transaction(
                user_id=user_id,
                account_id=transaction_data.account_id,
                transaction_id=transaction_id,
                transaction_type=transaction_data.transaction_type,
                amount=transaction_data.amount,
                currency=transaction_data.currency,
                description=transaction_data.description,
                reference_number=transaction_data.reference_number,
                merchant_info=transaction_data.merchant_info,
                status=TransactionStatus.PENDING
            )

            self.db.add(transaction)
            await self.db.commit()
            await self.db.refresh(transaction)

            # Update account balance
            await self.update_account_balance(
                transaction_data.account_id,
                transaction_data.amount,
                transaction_data.transaction_type
            )

            # Mark transaction as completed
            transaction.status = TransactionStatus.COMPLETED
            transaction.processed_at = datetime.utcnow()
            transaction.completed_at = datetime.utcnow()
            await self.db.commit()

            logger.info(f"Created transaction {transaction_id} for user {user_id}")
            return TransactionResponse.from_orm(transaction)

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating transaction for user {user_id}: {e}")
            raise

    async def get_transaction_history(
        self,
        user_id: int,
        account_id: Optional[int] = None,
        page: int = 1,
        page_size: int = 20
    ) -> TransactionHistory:
        """Get transaction history for a user."""
        try:
            # Build query
            query = select(Transaction).where(Transaction.user_id == user_id)

            if account_id:
                query = query.where(Transaction.account_id == account_id)

            # Get total count
            count_query = select(func.count(Transaction.id)).where(Transaction.user_id == user_id)
            if account_id:
                count_query = count_query.where(Transaction.account_id == account_id)

            total_result = await self.db.execute(count_query)
            total_count = total_result.scalar()

            # Get paginated results
            offset = (page - 1) * page_size
            query = query.order_by(desc(Transaction.created_at)).offset(offset).limit(page_size)

            result = await self.db.execute(query)
            transactions = result.scalars().all()

            return TransactionHistory(
                transactions=[TransactionResponse.from_orm(t) for t in transactions],
                total_count=total_count,
                page=page,
                page_size=page_size,
                has_next=offset + page_size < total_count,
                has_previous=page > 1
            )

        except Exception as e:
            logger.error(f"Error getting transaction history for user {user_id}: {e}")
            raise

    # ============================================================================
    # TRANSFER MANAGEMENT
    # ============================================================================

    async def create_transfer(self, user_id: int, transfer_data: TransferCreate) -> TransferResponse:
        """Create a money transfer between accounts."""
        try:
            # Verify both accounts belong to user
            from_account = await self.get_account(user_id, transfer_data.from_account_id)
            to_account = await self.get_account(user_id, transfer_data.to_account_id)

            if not from_account or not to_account:
                raise ValueError("One or both accounts not found or do not belong to user")

            # Check sufficient balance
            if from_account.balance < transfer_data.amount:
                raise ValueError("Insufficient funds for transfer")

            transfer_id = self._generate_transfer_id()

            transfer = Transfer(
                user_id=user_id,
                from_account_id=transfer_data.from_account_id,
                to_account_id=transfer_data.to_account_id,
                transfer_id=transfer_id,
                amount=transfer_data.amount,
                currency=transfer_data.currency,
                description=transfer_data.description,
                transfer_type=transfer_data.transfer_type,
                status=TransactionStatus.PENDING
            )

            self.db.add(transfer)
            await self.db.commit()
            await self.db.refresh(transfer)

            # Update account balances
            await self.update_account_balance(
                transfer_data.from_account_id,
                transfer_data.amount,
                TransactionType.WITHDRAWAL
            )
            await self.update_account_balance(
                transfer_data.to_account_id,
                transfer_data.amount,
                TransactionType.DEPOSIT
            )

            # Mark transfer as completed
            transfer.status = TransactionStatus.COMPLETED
            transfer.processed_at = datetime.utcnow()
            transfer.completed_at = datetime.utcnow()
            await self.db.commit()

            logger.info(f"Created transfer {transfer_id} for user {user_id}")
            return TransferResponse.from_orm(transfer)

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating transfer for user {user_id}: {e}")
            raise

    async def create_payment(self, user_id: int, payment_data: PaymentCreate) -> PaymentResponse:
        """Create a payment to external payee."""
        try:
            # Verify account belongs to user
            from_account = await self.get_account(user_id, payment_data.from_account_id)
            if not from_account:
                raise ValueError("Account not found or does not belong to user")

            # Check sufficient balance
            if from_account.balance < payment_data.amount:
                raise ValueError("Insufficient funds for payment")

            transaction_id = self._generate_transaction_id()

            # Create transaction record for the payment
            transaction = Transaction(
                user_id=user_id,
                account_id=payment_data.from_account_id,
                transaction_id=transaction_id,
                transaction_type=TransactionType.PAYMENT,
                amount=payment_data.amount,
                currency=payment_data.currency,
                description=payment_data.description,
                merchant_info={
                    "payee_account": payment_data.payee_account,
                    "payee_name": payment_data.payee_name,
                    "payment_type": payment_data.payment_type
                },
                status=TransactionStatus.PENDING
            )

            self.db.add(transaction)
            await self.db.commit()
            await self.db.refresh(transaction)

            # Update account balance
            await self.update_account_balance(
                payment_data.from_account_id,
                payment_data.amount,
                TransactionType.PAYMENT
            )

            # Mark transaction as completed
            transaction.status = TransactionStatus.COMPLETED
            transaction.processed_at = datetime.utcnow()
            transaction.completed_at = datetime.utcnow()
            await self.db.commit()

            # Create payment response
            payment_response = PaymentResponse(
                id=transaction.id,
                user_id=transaction.user_id,
                from_account_id=transaction.account_id,
                payee_account=payment_data.payee_account,
                payee_name=payment_data.payee_name,
                amount=transaction.amount,
                currency=transaction.currency,
                description=transaction.description,
                payment_type=payment_data.payment_type,
                status=transaction.status,
                created_at=transaction.created_at,
                processed_at=transaction.processed_at,
                completed_at=transaction.completed_at,
                risk_score=transaction.risk_score
            )

            logger.info(f"Created payment {transaction_id} for user {user_id}")
            return payment_response

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating payment for user {user_id}: {e}")
            raise

    # ============================================================================
    # DASHBOARD AND SUMMARY
    # ============================================================================

    async def get_account_summary(self, user_id: int) -> AccountSummaryResponse:
        """Get account summary for dashboard."""
        try:
            accounts = await self.get_user_accounts(user_id)

            # Calculate total balance
            total_balance = sum(account.balance for account in accounts)

            # Get recent transactions (last 10)
            recent_transactions_result = await self.db.execute(
                select(Transaction)
                .where(Transaction.user_id == user_id)
                .order_by(desc(Transaction.created_at))
                .limit(10)
            )
            recent_transactions = recent_transactions_result.scalars().all()

            return AccountSummaryResponse(
                total_accounts=len(accounts),
                total_balance=total_balance,
                accounts=accounts,
                recent_transactions=[TransactionResponse.from_orm(t) for t in recent_transactions]
            )

        except Exception as e:
            logger.error(f"Error getting account summary for user {user_id}: {e}")
            raise

    async def get_dashboard_summary(self, user_id: int) -> DashboardSummary:
        """Get comprehensive dashboard summary."""
        try:
            # Get account summary
            account_summary = await self.get_account_summary(user_id)

            # Calculate monthly spending and income
            current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            # Monthly spending (outgoing transactions)
            spending_result = await self.db.execute(
                select(func.coalesce(func.sum(Transaction.amount), 0))
                .where(
                    and_(
                        Transaction.user_id == user_id,
                        Transaction.transaction_type.in_([
                            TransactionType.WITHDRAWAL,
                            TransactionType.PAYMENT,
                            TransactionType.FEE
                        ]),
                        Transaction.created_at >= current_month_start
                    )
                )
            )
            monthly_spending = spending_result.scalar() or Decimal('0.00')

            # Monthly income (incoming transactions)
            income_result = await self.db.execute(
                select(func.coalesce(func.sum(Transaction.amount), 0))
                .where(
                    and_(
                        Transaction.user_id == user_id,
                        Transaction.transaction_type.in_([
                            TransactionType.DEPOSIT,
                            TransactionType.INTEREST
                        ]),
                        Transaction.created_at >= current_month_start
                    )
                )
            )
            monthly_income = income_result.scalar() or Decimal('0.00')

            # Spending by category (simplified)
            spending_by_category = {
                "Food & Dining": float(monthly_spending * Decimal('0.3')),
                "Shopping": float(monthly_spending * Decimal('0.2')),
                "Transportation": float(monthly_spending * Decimal('0.15')),
                "Bills & Utilities": float(monthly_spending * Decimal('0.2')),
                "Entertainment": float(monthly_spending * Decimal('0.1')),
                "Other": float(monthly_spending * Decimal('0.05'))
            }

            # Balance trend (last 7 days)
            balance_trend = []
            for i in range(7):
                date = datetime.now() - timedelta(days=i)
                balance_trend.append({
                    "date": date.strftime("%Y-%m-%d"),
                    "balance": float(account_summary.total_balance)  # Simplified
                })

            return DashboardSummary(
                total_balance=account_summary.total_balance,
                monthly_spending=monthly_spending,
                monthly_income=monthly_income,
                account_count=account_summary.total_accounts,
                recent_transactions=account_summary.recent_transactions[:5],
                spending_by_category=spending_by_category,
                balance_trend=balance_trend
            )

        except Exception as e:
            logger.error(f"Error getting dashboard summary for user {user_id}: {e}")
            raise

    # ============================================================================
    # DEMO DATA CREATION
    # ============================================================================

    async def create_demo_data(self, user_id: int) -> bool:
        """Create demo accounts and transactions for testing."""
        try:
            # Create demo accounts
            checking_account = await self.create_account(
                user_id,
                AccountCreate(
                    account_name="Primary Checking",
                    account_type=AccountType.CHECKING,
                    initial_balance=Decimal('5000.00'),
                    is_primary=True
                )
            )

            savings_account = await self.create_account(
                user_id,
                AccountCreate(
                    account_name="Emergency Savings",
                    account_type=AccountType.SAVINGS,
                    initial_balance=Decimal('15000.00')
                )
            )

            # Create demo transactions
            demo_transactions = [
                {
                    "account_id": checking_account.id,
                    "type": TransactionType.DEPOSIT,
                    "amount": Decimal('2500.00'),
                    "description": "Salary Deposit"
                },
                {
                    "account_id": checking_account.id,
                    "type": TransactionType.WITHDRAWAL,
                    "amount": Decimal('150.00'),
                    "description": "ATM Withdrawal"
                },
                {
                    "account_id": checking_account.id,
                    "type": TransactionType.PAYMENT,
                    "amount": Decimal('85.50'),
                    "description": "Grocery Store"
                },
                {
                    "account_id": savings_account.id,
                    "type": TransactionType.INTEREST,
                    "amount": Decimal('25.00'),
                    "description": "Monthly Interest"
                }
            ]

            for tx_data in demo_transactions:
                await self.create_transaction(
                    user_id,
                    TransactionCreate(
                        account_id=tx_data["account_id"],
                        transaction_type=tx_data["type"],
                        amount=tx_data["amount"],
                        description=tx_data["description"]
                    )
                )

            logger.info(f"Created demo data for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error creating demo data for user {user_id}: {e}")
            return False
