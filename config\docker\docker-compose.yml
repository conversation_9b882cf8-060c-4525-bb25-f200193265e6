# TrustChain-Auth Docker Compose Configuration
# Centralized configuration for all services

services:
  # Trust<PERSON>hain-Auth API
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************************/trustchain_db
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET_KEY=your-secret-key-change-in-production
      - ENVIRONMENT=development
      - MODEL_SERVER_URL=http://ml-service:8001
    depends_on:
      - postgres
      - redis
      - ml-service
    volumes:
      - ./backend:/app
    restart: unless-stopped
    networks:
      - trustchain-network

  # ML Service
  ml-service:
    build:
      context: ./ml_models
      dockerfile: Dockerfile
    ports:
      - "8001:8000"
    environment:
      - MODEL_PATH=/app/models/v1
      - TF_CPP_MIN_LOG_LEVEL=2
      - ENVIRONMENT=development
    volumes:
      - ./ml_models:/app
    restart: unless-stopped
    networks:
      - trustchain-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=trustchain_db
      - POSTGRES_USER=trustchain
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - type: bind
        source: D:/docker-data/trustchain/postgres
        target: /var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - trustchain-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - type: bind
        source: D:/docker-data/trustchain/redis
        target: /data
    restart: unless-stopped
    networks:
      - trustchain-network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus:/etc/prometheus
      - type: bind
        source: D:/docker-data/trustchain/prometheus
        target: /prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - trustchain-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    volumes:
      - type: bind
        source: D:/docker-data/trustchain/grafana
        target: /var/lib/grafana
    restart: unless-stopped
    networks:
      - trustchain-network

networks:
  trustchain-network:
    driver: bridge
