# TrustChain-Auth Mobile App Configuration

# API Configuration
api:
  base_url: "http://192.168.29.203:8000"
  timeout_seconds: 30
  retry_attempts: 3
  
# Authentication Configuration
auth:
  token_storage_key: "trustchain_auth_token"
  biometric_enabled: true
  session_timeout_minutes: 60
  
# Behavioral Biometrics Configuration
behavioral:
  collection_enabled: true
  sampling_rate_hz: 100
  buffer_size: 1000
  upload_interval_seconds: 30
  
# Security Configuration
security:
  certificate_pinning: true
  encryption_enabled: true
  secure_storage: true
  
# UI Configuration
ui:
  theme: "light"
  animations_enabled: true
  haptic_feedback: true
  
# Logging Configuration
logging:
  level: "INFO"
  local_storage: true
  remote_logging: false
  
# Feature Flags
features:
  ml_inference_enabled: true
  offline_mode: false
  debug_mode: false
