# This is a generated file; do not edit or check into version control.
battery_plus=D:\\pub-cache\\hosted\\pub.dev\\battery_plus-4.1.0\\
device_info_plus=D:\\pub-cache\\hosted\\pub.dev\\device_info_plus-9.1.2\\
flutter_plugin_android_lifecycle=D:\\pub-cache\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.26\\
flutter_secure_storage=D:\\pub-cache\\hosted\\pub.dev\\flutter_secure_storage-9.2.4\\
flutter_secure_storage_linux=D:\\pub-cache\\hosted\\pub.dev\\flutter_secure_storage_linux-1.2.3\\
flutter_secure_storage_macos=D:\\pub-cache\\hosted\\pub.dev\\flutter_secure_storage_macos-3.1.3\\
flutter_secure_storage_web=D:\\pub-cache\\hosted\\pub.dev\\flutter_secure_storage_web-1.2.1\\
flutter_secure_storage_windows=D:\\pub-cache\\hosted\\pub.dev\\flutter_secure_storage_windows-3.1.2\\
integration_test=C:\\Users\\<USER>\\flutter\\packages\\integration_test\\
local_auth=D:\\pub-cache\\hosted\\pub.dev\\local_auth-2.3.0\\
local_auth_android=D:\\pub-cache\\hosted\\pub.dev\\local_auth_android-1.0.47\\
local_auth_darwin=D:\\pub-cache\\hosted\\pub.dev\\local_auth_darwin-1.4.3\\
local_auth_windows=D:\\pub-cache\\hosted\\pub.dev\\local_auth_windows-1.0.11\\
path_provider=D:\\pub-cache\\hosted\\pub.dev\\path_provider-2.1.5\\
path_provider_android=D:\\pub-cache\\hosted\\pub.dev\\path_provider_android-2.2.15\\
path_provider_foundation=D:\\pub-cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\
path_provider_linux=D:\\pub-cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\
path_provider_windows=D:\\pub-cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\
permission_handler=D:\\pub-cache\\hosted\\pub.dev\\permission_handler-10.4.5\\
permission_handler_android=D:\\pub-cache\\hosted\\pub.dev\\permission_handler_android-10.3.6\\
permission_handler_apple=D:\\pub-cache\\hosted\\pub.dev\\permission_handler_apple-9.1.4\\
permission_handler_windows=D:\\pub-cache\\hosted\\pub.dev\\permission_handler_windows-0.1.3\\
sensors_plus=D:\\pub-cache\\hosted\\pub.dev\\sensors_plus-3.1.0\\
shared_preferences=D:\\pub-cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\
shared_preferences_android=D:\\pub-cache\\hosted\\pub.dev\\shared_preferences_android-2.4.7\\
shared_preferences_foundation=D:\\pub-cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\
shared_preferences_linux=D:\\pub-cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\
shared_preferences_web=D:\\pub-cache\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\
shared_preferences_windows=D:\\pub-cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\
tflite_flutter=D:\\pub-cache\\hosted\\pub.dev\\tflite_flutter-0.11.0\\
