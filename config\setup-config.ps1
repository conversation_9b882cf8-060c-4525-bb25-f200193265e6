# TrustChain-Auth Configuration Setup Script
# This script sets up configuration files for different environments

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("development", "production", "testing")]
    [string]$Environment = "development"
)

Write-Host "Setting up TrustChain-Auth configuration for: $Environment" -ForegroundColor Green

# Create environment-specific directories
$envDirs = @("backend", "mobile", "ml-service", "docker")
foreach ($dir in $envDirs) {
    $envPath = Join-Path $PSScriptRoot "$dir/$Environment"
    if (!(Test-Path $envPath)) {
        New-Item -ItemType Directory -Path $envPath -Force | Out-Null
        Write-Host "Created directory: $envPath" -ForegroundColor Yellow
    }
}

# Copy environment files if they don't exist
$configFiles = @{
    "backend/.env.example" = "backend/$Environment/.env"
    "ml-service/.env.example" = "ml-service/$Environment/.env"
    "mobile/app_config.yaml" = "mobile/$Environment/app_config.yaml"
}

foreach ($source in $configFiles.Keys) {
    $sourcePath = Join-Path $PSScriptRoot $source
    $destPath = Join-Path $PSScriptRoot $configFiles[$source]
    
    if (!(Test-Path $destPath) -and (Test-Path $sourcePath)) {
        Copy-Item $sourcePath $destPath -Force
        Write-Host "Created config file: $destPath" -ForegroundColor Yellow
    }
}

# Update docker-compose configuration
$dockerComposePath = Join-Path $PSScriptRoot "../docker-compose.yml"
$configDockerComposePath = Join-Path $PSScriptRoot "docker/docker-compose.yml"

if (Test-Path $configDockerComposePath) {
    Copy-Item $configDockerComposePath $dockerComposePath -Force
    Write-Host "Updated docker-compose.yml from centralized config" -ForegroundColor Green
}

Write-Host "Configuration setup complete for $Environment environment!" -ForegroundColor Green
Write-Host "Please review and update the configuration files as needed." -ForegroundColor Cyan
