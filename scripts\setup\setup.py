"""Package setup."""
from setuptools import setup, find_packages

setup(
    name="trustchain_auth",
    version="1.0.0",
    packages=find_packages(),
    include_package_data=True,
    install_requires=[
        # FastAPI Backend Dependencies
        "fastapi==0.115.13",
        "uvicorn[standard]==0.34.3",
        "pydantic==2.11.7",
        "pydantic-settings==2.10.0",
        "pydantic[email]",
        "email-validator>=2.0.0",

        # Database
        "sqlalchemy==2.0.23",
        "alembic==1.13.1",
        "psycopg2-binary==2.9.9",
        "asyncpg==0.29.0",
        "aiosqlite==0.19.0",

        # Authentication & Security
        "python-jose[cryptography]==3.3.0",
        "passlib[bcrypt]==1.7.4",
        "python-multipart==0.0.6",
        "bcrypt==4.1.2",
        "cryptography==41.0.7",

        # HTTP Client
        "httpx==0.25.2",
        "aiohttp==3.9.1",

        # Monitoring & Logging
        "prometheus-client==0.22.1",
        "structlog==25.4.0",
        "sentry-sdk[fastapi]==2.30.0",
        "python-json-logger==3.3.0",

        # Configuration
        "python-dotenv==1.1.0",
        "pyyaml==6.0.2",

        # ML Dependencies
        "tensorflow==2.14.0",
        "scikit-learn==1.3.2",
        "numpy==1.24.3",
        "pandas==2.1.1",
    ],
    extras_require={
        "test": [
            "pytest==7.4.3",
            "pytest-asyncio==0.21.1",
            "pytest-cov==4.1.0",
            "aiosqlite==0.19.0",
            "httpx==0.25.2",
            "propcache==0.3.2",
            "coverage==7.9.1",
        ],
        "dev": [
            "black",
            "flake8",
            "isort",
            "mypy",
            "pre-commit",
        ]
    }
)
