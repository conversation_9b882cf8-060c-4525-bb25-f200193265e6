"""Banking schemas for API requests and responses."""

from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field, validator
from db.models import AccountType, TransactionType, TransactionStatus


# ============================================================================
# ACCOUNT SCHEMAS
# ============================================================================

class AccountBase(BaseModel):
    """Base account schema."""
    account_name: str = Field(..., description="User-friendly account name")
    account_type: AccountType
    currency: str = Field(default="USD", description="Account currency")
    is_primary: bool = Field(default=False, description="Is this the primary account")

class AccountCreate(AccountBase):
    """Schema for creating a new account."""
    initial_balance: Optional[Decimal] = Field(default=Decimal('0.00'), description="Initial account balance")

class AccountUpdate(BaseModel):
    """Schema for updating account information."""
    account_name: Optional[str] = None
    is_primary: Optional[bool] = None
    is_active: Optional[bool] = None

class AccountResponse(AccountBase):
    """Schema for account responses."""
    id: int
    user_id: int
    account_number: str
    balance: Decimal
    is_active: bool
    created_at: datetime
    updated_at: datetime
    routing_number: Optional[str] = None
    bank_name: Optional[str] = None

    class Config:
        from_attributes = True

class AccountSummary(BaseModel):
    """Summary schema for account overview."""
    id: int
    account_name: str
    account_type: AccountType
    account_number: str
    balance: Decimal
    currency: str
    is_primary: bool

    class Config:
        from_attributes = True


# ============================================================================
# TRANSACTION SCHEMAS
# ============================================================================

class TransactionBase(BaseModel):
    """Base transaction schema."""
    amount: Decimal = Field(..., gt=0, description="Transaction amount")
    currency: str = Field(default="USD", description="Transaction currency")
    description: Optional[str] = Field(None, description="Transaction description")

class TransactionCreate(TransactionBase):
    """Schema for creating a transaction."""
    account_id: int
    transaction_type: TransactionType
    reference_number: Optional[str] = None
    merchant_info: Optional[dict] = None

class TransactionResponse(TransactionBase):
    """Schema for transaction responses."""
    id: int
    user_id: int
    account_id: int
    transaction_id: str
    transaction_type: TransactionType
    status: TransactionStatus
    reference_number: Optional[str] = None
    created_at: datetime
    processed_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    risk_score: Optional[float] = None

    class Config:
        from_attributes = True

class TransactionHistory(BaseModel):
    """Schema for transaction history with pagination."""
    transactions: List[TransactionResponse]
    total_count: int
    page: int
    page_size: int
    has_next: bool
    has_previous: bool


# ============================================================================
# TRANSFER SCHEMAS
# ============================================================================

class TransferBase(BaseModel):
    """Base transfer schema."""
    amount: Decimal = Field(..., gt=0, description="Transfer amount")
    currency: str = Field(default="USD", description="Transfer currency")
    description: Optional[str] = Field(None, description="Transfer description")

class TransferCreate(TransferBase):
    """Schema for creating a transfer."""
    from_account_id: int
    to_account_id: int
    transfer_type: Optional[str] = Field(default="internal", description="Type of transfer")

    @validator('from_account_id', 'to_account_id')
    def validate_account_ids(cls, v):
        if v <= 0:
            raise ValueError('Account ID must be positive')
        return v

    @validator('to_account_id')
    def validate_different_accounts(cls, v, values):
        if 'from_account_id' in values and v == values['from_account_id']:
            raise ValueError('Cannot transfer to the same account')
        return v

class TransferResponse(TransferBase):
    """Schema for transfer responses."""
    id: int
    user_id: int
    from_account_id: int
    to_account_id: int
    transfer_id: str
    status: TransactionStatus
    transfer_type: str
    fees: Decimal
    created_at: datetime
    processed_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    risk_score: Optional[float] = None

    class Config:
        from_attributes = True


# ============================================================================
# PAYMENT SCHEMAS
# ============================================================================

class PaymentCreate(BaseModel):
    """Schema for creating a payment."""
    from_account_id: int
    payee_account: str = Field(..., description="Payee account number or identifier")
    amount: Decimal = Field(..., gt=0, description="Payment amount")
    currency: str = Field(default="USD", description="Payment currency")
    description: Optional[str] = Field(None, description="Payment description")
    payee_name: Optional[str] = Field(None, description="Payee name")
    payment_type: Optional[str] = Field(default="bill_pay", description="Type of payment")

class PaymentResponse(BaseModel):
    """Schema for payment responses."""
    id: int
    user_id: int
    from_account_id: int
    payee_account: str
    payee_name: Optional[str] = None
    amount: Decimal
    currency: str
    description: Optional[str] = None
    payment_type: str
    status: TransactionStatus
    created_at: datetime
    processed_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    risk_score: Optional[float] = None

    class Config:
        from_attributes = True


# ============================================================================
# BALANCE AND SUMMARY SCHEMAS
# ============================================================================

class BalanceResponse(BaseModel):
    """Schema for account balance response."""
    account_id: int
    account_name: str
    account_type: AccountType
    balance: Decimal
    currency: str
    available_balance: Decimal
    pending_balance: Decimal
    last_updated: datetime

class AccountSummaryResponse(BaseModel):
    """Schema for account summary response."""
    total_accounts: int
    total_balance: Decimal
    accounts: List[AccountSummary]
    recent_transactions: List[TransactionResponse]

class DashboardSummary(BaseModel):
    """Schema for dashboard summary."""
    total_balance: Decimal
    monthly_spending: Decimal
    monthly_income: Decimal
    account_count: int
    recent_transactions: List[TransactionResponse]
    spending_by_category: dict
    balance_trend: List[dict]
