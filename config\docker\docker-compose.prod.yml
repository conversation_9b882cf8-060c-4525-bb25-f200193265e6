# Production environment configuration
# Use with: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up

services:
  api:
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - ENVIRONMENT=production
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
          
  ml-service:
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - ENVIRONMENT=production
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
          
  postgres:
    environment:
      - POSTGRES_DB=trustchain_db_prod
    restart: unless-stopped
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
          
  redis:
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

volumes:
  postgres_data_prod:
    driver: local
