"""Authentication event and security alert models."""

from sqlalchemy import Column, Integer, String, Float, JSON, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime

from db.base_class import Base

class AuthEvent(Base):
    """Model for authentication events."""
    __tablename__ = "auth_events"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), index=True)
    event_type = Column(String)  # login_attempt, risk_assessment, challenge_triggered, etc.
    risk_score = Column(Float)
    timestamp = Column(DateTime, default=datetime.utcnow)
    device_info = Column(JSON)
    location = Column(JSON)
    success = Column(Boolean, default=False)
    
    # Relationships
    user = relationship("User", back_populates="auth_events")
    device = relationship("Device", back_populates="auth_events")

class SecurityAlert(Base):
    """Model for security alerts."""
    __tablename__ = "security_alerts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), nullable=True)
    alert_type = Column(String)  # high_risk, panic_gesture, suspicious_activity
    severity = Column(String)  # low, medium, high, critical
    description = Column(String)
    timestamp = Column(DateTime, default=datetime.utcnow)
    device_info = Column(JSON, nullable=True)
    is_resolved = Column(Boolean, default=False)
    resolution_notes = Column(String, nullable=True)
    resolved_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="security_alerts")
    device = relationship("Device", back_populates="security_alerts")
