# Development environment overrides
# This file is automatically loaded by docker-compose for development

services:
  api:
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    volumes:
      - ../../backend:/app:ro
    ports:
      - "8000:8000"
      
  ml-service:
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    volumes:
      - ../../ml_models:/app:ro
    ports:
      - "8001:8000"
      
  postgres:
    environment:
      - POSTGRES_DB=trustchain_db_dev
    ports:
      - "5432:5432"
      
  redis:
    ports:
      - "6379:6379"
      
  grafana:
    ports:
      - "3000:3000"
      
  prometheus:
    ports:
      - "9090:9090"
