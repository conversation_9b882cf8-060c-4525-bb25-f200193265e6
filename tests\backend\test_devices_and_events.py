"""Test devices and events."""
import os
import sys
from pathlib import Path
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict
from datetime import datetime

# Make sure backend is in Python path
backend_path = str(Path(__file__).parent.parent / "backend")
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

from services.auth import AuthService
from services.behavioral import BehavioralService
from schemas.device import Devi<PERSON><PERSON>reate
from schemas.behavioral import BehavioralEventCreate
from db.models import User, Device, BehavioralEvent

@pytest.mark.asyncio
async def test_register_device(test_session: AsyncSession):
    """Test device registration."""
    auth_service = AuthService(test_session)
    # Create test user
    user = User(
        email="<EMAIL>",
        first_name="Device",
        last_name="Test",
        password_hash="hashedpass"
    )
    test_session.add(user)
    await test_session.commit()
    
    # Register device
    device_data = DeviceCreate(
        device_id="test-device",
        device_type="mobile",
        device_info={"os": "android"}
    )
    # Note the parameter order: first user_id, then device_data
    device = await auth_service.register_device(user.id, device_data)
    
    # Verify device creation
    assert device.device_id == device_data.device_id
    assert device.user_id == user.id

@pytest.mark.asyncio
async def test_auth_events(test_session: AsyncSession):
    """Test auth event creation and querying."""
    auth_service = AuthService(test_session)
    # Create test user and device
    user = User(
        email="<EMAIL>",
        first_name="Events",
        last_name="Test",
        password_hash="hashedpass"
    )
    test_session.add(user)
    await test_session.commit()

    device_data = DeviceCreate(
        device_id="events-device",
        device_type="mobile",
        device_info={"os": "android"}
    )
    # Note the parameter order: first user_id, then device_data
    device = await auth_service.register_device(user.id, device_data)
    
    # Create auth event
    event_data = AuthEventCreate(
        device_id=device.id,
        event_type="login",
        event_data={"ip": "127.0.0.1"},
        timestamp=datetime.utcnow()
    )
    event = await auth_service.log_auth_event(user.id, event_data)
    assert event.device_id == event_data.device_id
    assert event.event_type == event_data.event_type
