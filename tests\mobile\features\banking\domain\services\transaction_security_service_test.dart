import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:trustchain_auth/features/auth/domain/services/continuous_auth_service.dart';
import 'package:trustchain_auth/features/auth/domain/services/security_policy_service.dart';
import 'package:trustchain_auth/features/auth/domain/models/security_score.dart';
import 'package:trustchain_auth/features/banking/domain/services/transaction_security_service.dart';
import 'package:trustchain_auth/features/banking/domain/models/models.dart';

import 'transaction_security_service_test.mocks.dart';

@GenerateMocks([ContinuousAuthService, SecurityPolicyService])
void main() {
  late TransactionSecurityService service;
  late MockContinuousAuthService mockContinuousAuthService;
  late MockSecurityPolicyService mockSecurityPolicyService;

  setUp(() {
    mockContinuousAuthService = MockContinuousAuthService();
    mockSecurityPolicyService = MockSecurityPolicyService();
    service = TransactionSecurityService(
      mockContinuousAuthService,
      mockSecurityPolicyService,
    );
  });

  group('TransactionSecurityService', () {
    test('should assess low risk for small amounts with good security score', () async {
      // Arrange
      final transfer = TransferRequest(
        fromAccountId: 'from123',
        toAccountId: 'to456',
        amount: 50.0,
        currency: 'USD',
        description: 'Test transfer',
      );
      final securityScore = const SecurityScore(
        score: 0.9,
        riskLevel: RiskLevel.low,
        lastUpdated: null,
      );
      when(mockContinuousAuthService.getCurrentConfidenceLevel())
          .thenAnswer((_) async => 0.95);

      // Act
      final riskLevel = await service.assessTransactionRisk(transfer, securityScore);

      // Assert
      expect(riskLevel, TransactionRiskLevel.low);
    });

    test('should assess high risk for large amounts', () async {
      // Arrange
      final transfer = TransferRequest(
        fromAccountId: 'from123',
        toAccountId: 'to456',
        amount: 1500.0,
        currency: 'USD',
        description: 'Large transfer',
      );
      final securityScore = const SecurityScore(
        score: 0.9,
        riskLevel: RiskLevel.low,
        lastUpdated: null,
      );
      when(mockContinuousAuthService.getCurrentConfidenceLevel())
          .thenAnswer((_) async => 0.95);

      // Act
      final riskLevel = await service.assessTransactionRisk(transfer, securityScore);

      // Assert
      expect(riskLevel, TransactionRiskLevel.high);
    });

    test('should elevate risk level when confidence is low', () async {
      // Arrange
      final transfer = TransferRequest(
        fromAccountId: 'from123',
        toAccountId: 'to456',
        amount: 50.0,
        currency: 'USD',
        description: 'Low confidence transfer',
      );
      final securityScore = const SecurityScore(
        score: 0.9,
        riskLevel: RiskLevel.low,
        lastUpdated: null,
      );
      when(mockContinuousAuthService.getCurrentConfidenceLevel())
          .thenAnswer((_) async => 0.4);

      // Act
      final riskLevel = await service.assessTransactionRisk(transfer, securityScore);

      // Assert
      expect(riskLevel, TransactionRiskLevel.medium);
    });

    test('should require verification for medium risk when policy enables it', () async {
      // Arrange
      when(mockSecurityPolicyService.requireChallengeForMediumRisk)
          .thenReturn(true);

      // Act
      final requiresVerification = await service.requiresAdditionalVerification(
        TransactionRiskLevel.medium,
      );

      // Assert
      expect(requiresVerification, true);
    });

    test('should not require verification for medium risk when policy disables it', () async {
      // Arrange
      when(mockSecurityPolicyService.requireChallengeForMediumRisk)
          .thenReturn(false);

      // Act
      final requiresVerification = await service.requiresAdditionalVerification(
        TransactionRiskLevel.medium,
      );

      // Assert
      expect(requiresVerification, false);
    });

    test('should always require verification for high risk', () async {
      // Act
      final requiresVerification = await service.requiresAdditionalVerification(
        TransactionRiskLevel.high,
      );

      // Assert
      expect(requiresVerification, true);
    });

    test('should require all verification methods for critical risk', () async {
      // Arrange
      when(mockSecurityPolicyService.isBiometricVerificationEnabled)
          .thenReturn(true);

      // Act
      final methods = await service.getRequiredVerificationMethods(
        TransactionRiskLevel.critical,
      );

      // Assert
      expect(methods, containsAll(['biometric', 'pin', 'behavioral']));
    });
  });
}
