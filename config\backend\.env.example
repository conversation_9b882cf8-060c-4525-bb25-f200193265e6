# TrustChain-Auth Backend Configuration
# Copy this file to .env and update values for your environment

# Database Configuration
DATABASE_URL=postgresql+asyncpg://trustchain:password@postgres:5432/trustchain_db
SQLALCHEMY_DATABASE_URI=postgresql+asyncpg://trustchain:password@postgres:5432/trustchain_db

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# JWT Configuration
SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=TrustChain-Auth
VERSION=1.0.0
DESCRIPTION=Behavioral Biometrics Banking Authentication System

# CORS Configuration
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8080","http://localhost:8000","http://**************:8000"]

# ML Service Configuration
ML_SERVICE_URL=http://ml-service:8000
ML_SERVICE_TIMEOUT=30

# Monitoring Configuration
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_TIMEOUT_MINUTES=60

# Development/Production Mode
ENVIRONMENT=development
DEBUG=true
