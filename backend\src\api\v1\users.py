"""User related routes."""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from core.deps import get_current_user
from core.logging_config import logger
from schemas.user import User, UserUpdate
from schemas.behavioral import BehavioralProfile
from services.auth import AuthService
from db.models import User as UserModel
from db.session import get_async_session

router = APIRouter(prefix="/api/v1/users", tags=["users"])

@router.get("/me", response_model=User)
async def get_user_me(
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> UserModel:
    """Get current user."""
    return current_user

@router.put("/me", response_model=User)
async def update_user_me(
    user_data: UserUpdate,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> UserModel:
    """Update current user."""
    auth_service = AuthService(db)
    return await auth_service.update_user(current_user.id, user_data)

@router.delete("/me", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user_me(
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> None:
    """Delete current user."""
    auth_service = AuthService(db)
    await auth_service.delete_user(current_user.id)

@router.get("/me/behavioral_profile", response_model=List[BehavioralProfile])
async def get_user_behavioral_profile(
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> List[BehavioralProfile]:
    """Get user's behavioral profiles."""
    auth_service = AuthService(db)
    return await auth_service.get_behavioral_profiles(current_user.id)
