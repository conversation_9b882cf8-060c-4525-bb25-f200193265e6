"""Consolidated database models for TrustChain-Auth."""

import enum
from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, JSON, DateTime, ForeignKey, Boolean, Enum as SQLAEnum
from sqlalchemy.orm import relationship

from db.base_class import Base

# ============================================================================
# ENUMS
# ============================================================================

class UserRole(str, enum.Enum):
    ADMIN = "admin"
    USER = "user"
    DEVICE = "device"

class DeviceType(str, enum.Enum):
    MOBILE = "mobile"
    DESKTOP = "desktop"
    TABLET = "tablet"
    WEB = "web"

class Platform(str, enum.Enum):
    IOS = "ios"
    ANDROID = "android"
    WINDOWS = "windows"
    MACOS = "macos"
    LINUX = "linux"
    WEB = "web"

class EventType(str, enum.Enum):
    LOGIN_ATTEMPT = "login_attempt"
    RISK_ASSESSMENT = "risk_assessment"
    CHALLENGE_TRIGGERED = "challenge_triggered"
    LOGOUT = "logout"
    DEVICE_REGISTERED = "device_registered"

# ============================================================================
# CORE MODELS
# ============================================================================

class User(Base):
    """User model for authentication and user data."""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    password_hash = Column(String)
    first_name = Column(String)
    last_name = Column(String)
    full_name = Column(String)
    role = Column(SQLAEnum(UserRole), default=UserRole.USER)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    devices = relationship("Device", back_populates="user")
    auth_events = relationship("AuthEvent", back_populates="user")
    security_alerts = relationship("SecurityAlert", back_populates="user")
    behavioral_events = relationship("BehavioralEvent", back_populates="user")
    behavioral_features = relationship("BehavioralFeature", back_populates="user")
    behavioral_profile = relationship("BehavioralProfile", back_populates="user", uselist=False)
    risk_assessments = relationship("RiskAssessment", back_populates="user")

class Device(Base):
    """Device model for managing user devices."""
    __tablename__ = "devices"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_type = Column(SQLAEnum(DeviceType))
    device_id = Column(String, unique=True, index=True)  # Unique device identifier
    device_name = Column(String)  # User-friendly device name
    platform = Column(SQLAEnum(Platform))
    last_used = Column(DateTime, default=datetime.utcnow)
    is_trusted = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    device_info = Column(JSON)  # Additional device information
    
    # Relationships
    user = relationship("User", back_populates="devices")
    auth_events = relationship("AuthEvent", back_populates="device")
    security_alerts = relationship("SecurityAlert", back_populates="device")
    behavioral_events = relationship("BehavioralEvent", back_populates="device")
    behavioral_features = relationship("BehavioralFeature", back_populates="device")
    risk_assessments = relationship("RiskAssessment", back_populates="device")

# ============================================================================
# AUTHENTICATION MODELS
# ============================================================================

class AuthEvent(Base):
    """Model for authentication events."""
    __tablename__ = "auth_events"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), index=True)
    event_type = Column(SQLAEnum(EventType))
    risk_score = Column(Float)
    timestamp = Column(DateTime, default=datetime.utcnow)
    device_info = Column(JSON)
    location = Column(JSON)
    success = Column(Boolean, default=False)
    
    # Relationships
    user = relationship("User", back_populates="auth_events")
    device = relationship("Device", back_populates="auth_events")

class SecurityAlert(Base):
    """Model for security alerts."""
    __tablename__ = "security_alerts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), nullable=True)
    alert_type = Column(String)  # high_risk, panic_gesture, suspicious_activity
    severity = Column(String)  # low, medium, high, critical
    description = Column(String)
    timestamp = Column(DateTime, default=datetime.utcnow)
    device_info = Column(JSON, nullable=True)
    is_resolved = Column(Boolean, default=False)
    resolution_notes = Column(String, nullable=True)
    resolved_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="security_alerts")
    device = relationship("Device", back_populates="security_alerts")

# ============================================================================
# BEHAVIORAL BIOMETRICS MODELS
# ============================================================================

class BehavioralEvent(Base):
    """Behavioral events like typing and touch interactions."""
    __tablename__ = "behavioral_events"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), index=True)
    session_id = Column(String, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    event_type = Column(String)  # 'typing', 'touch', etc.
    data = Column(JSON)  # Event-specific data
    context = Column(JSON)  # Device context, app state, etc.

    # Relationships
    user = relationship("User", back_populates="behavioral_events")
    device = relationship("Device", back_populates="behavioral_events")

class BehavioralFeature(Base):
    """Extracted behavioral features."""
    __tablename__ = "behavioral_features"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), index=True)
    session_id = Column(String, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    feature_type = Column(String)  # 'typing', 'touch', 'combined'
    features = Column(JSON)  # Extracted feature vector
    feature_metadata = Column(JSON)  # Feature extraction metadata

    # Relationships
    user = relationship("User", back_populates="behavioral_features")
    device = relationship("Device", back_populates="behavioral_features")

class BehavioralProfile(Base):
    """User's behavioral profile."""
    __tablename__ = "behavioral_profiles"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)

    # Profile data
    profile_type = Column(String)  # 'baseline', 'current', etc.
    latent_features = Column(JSON)  # Encoded behavioral features
    metrics = Column(JSON)  # Profile quality metrics
    thresholds = Column(JSON)  # Anomaly detection thresholds
    profile_metadata = Column(JSON)  # Training metadata, version info, etc.

    # Relationships
    user = relationship("User", back_populates="behavioral_profile")

class RiskAssessment(Base):
    """Behavioral risk assessments."""
    __tablename__ = "risk_assessments"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), index=True)
    session_id = Column(String, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow)

    # Risk scores
    risk_score = Column(Float)
    anomaly_score = Column(Float)
    confidence = Column(Float)

    # Assessment details
    features_used = Column(JSON)  # List of features used
    model_version = Column(String)  # Model version used
    decision = Column(String)  # 'allow', 'block', 'challenge'
    explanation = Column(JSON)  # Decision explanation
    assessment_metadata = Column(JSON)  # Additional assessment metadata

    # Relationships
    user = relationship("User", back_populates="risk_assessments")
    device = relationship("Device", back_populates="risk_assessments")
