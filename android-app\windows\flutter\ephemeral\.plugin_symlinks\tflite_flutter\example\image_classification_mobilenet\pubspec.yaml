name: image_classification_mobilenet
description: Image Classification using mobilenet.

publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ">=3.3.0 <4.0.0"

dependencies:
  camera: ^0.10.5+2
  flutter:
    sdk: flutter
  image: ^4.0.17
  path: ^1.8.3
  path_provider: ^2.0.15
  image_picker: ^0.8.8
  tflite_flutter:
    path: ../../

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  build_runner: ^2.4.4
  flutter_launcher_icons:

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/models/

flutter_launcher_icons:
  image_path: "assets/images/logo.png"
  android: "ic_launcher"
  ios: true
  remove_alpha_ios: true
