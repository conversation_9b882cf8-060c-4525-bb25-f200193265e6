"""Authentication related routes."""

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from schemas.auth import AuthEventCreate
from schemas.token import Token
from schemas.user import UserCreate, User
from schemas.device import Device, DeviceCreate
from services.auth import AuthService
from core.deps import get_current_user, get_current_user_id
from db.session import get_async_session

router = APIRouter(prefix="/api/v1/auth", tags=["auth"])

@router.post("/register", status_code=status.HTTP_201_CREATED, response_model=User)
async def register_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_async_session)
) -> User:
    """Register a new user."""
    try:
        auth_service = AuthService(db)
        user = await auth_service.create_user(user_data)
        return user
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post("/login", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_async_session)
) -> Token:
    """Authenticate user and return access token."""
    auth_service = AuthService(db)
    token = await auth_service.authenticate_user(form_data.username, form_data.password)
    return token

@router.post("/device/register", response_model=Device)
async def register_device(
    device_data: DeviceCreate,
    current_user_id: int = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_async_session)
) -> Device:
    """Register a new device for the current user."""
    auth_service = AuthService(db)
    return await auth_service.register_device(current_user_id, device_data)

@router.post("/event", status_code=status.HTTP_201_CREATED)
async def log_auth_event(
    event_data: AuthEventCreate,
    current_user_id: int = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_async_session)
):
    """Log an authentication event."""
    auth_service = AuthService(db)
    await auth_service.log_auth_event(current_user_id, event_data)
    return {"message": "Event logged successfully"}
