"""Authentication and security alert schemas."""

from typing import Dict, Optional
from datetime import datetime
from pydantic import BaseModel, ConfigDict

class Token(BaseModel):
    """Token schema for access tokens."""
    access_token: str
    token_type: str = "bearer"

class TokenData(BaseModel):
    """Token payload data."""
    user_id: Optional[str] = None

class AuthEventBase(BaseModel):
    event_type: str
    risk_score: float
    device_info: Optional[Dict] = None
    location: Optional[Dict] = None
    success: bool

class AuthEventCreate(AuthEventBase):
    pass

class AuthEventResponse(AuthEventBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    user_id: int
    device_id: Optional[int] = None
    timestamp: datetime

class SecurityAlertBase(BaseModel):
    user_id: int
    alert_type: str
    severity: str
    description: str
    device_info: Optional[Dict] = None

class SecurityAlertCreate(SecurityAlertBase):
    pass

class SecurityAlertResponse(SecurityAlertBase):
    model_config = ConfigDict(from_attributes=True)

    id: int
    timestamp: datetime
    is_resolved: bool
    resolution_notes: Optional[str] = None
    resolved_at: Optional[datetime] = None
