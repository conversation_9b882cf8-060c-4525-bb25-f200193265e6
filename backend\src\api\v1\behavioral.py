"""Behavioral data related routes."""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from core.deps import get_current_user
from schemas.behavioral import (
    BehavioralData,
    BehavioralDataCreate,
    BehavioralProfile,
    BehavioralScore,
    BehavioralTrigger
)
from services.behavioral import BehavioralService
from db.models import User as UserModel
from db.session import get_async_session

router = APIRouter(prefix="/api/v1/behavioral", tags=["behavioral"])

@router.post("/collect", status_code=status.HTTP_201_CREATED)
async def collect_behavioral_data(
    data: BehavioralDataCreate,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> dict:
    """Collect user behavioral data."""
    behavioral_service = BehavioralService(db)
    await behavioral_service.collect_data(data, current_user.id)
    return {"message": "Data collected successfully"}

@router.get("/data", response_model=List[BehavioralData])
async def get_behavioral_data(
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> List[BehavioralData]:
    """Get user's behavioral data."""
    behavioral_service = BehavioralService(db)
    return await behavioral_service.get_user_data(current_user.id)

@router.get("/score", response_model=BehavioralScore)
async def get_behavioral_score(
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> BehavioralScore:
    """Get user's behavioral risk score."""
    behavioral_service = BehavioralService(db)
    return await behavioral_service.calculate_score(current_user.id)

@router.post("/trigger", status_code=status.HTTP_201_CREATED)
async def trigger_behavioral_check(
    trigger: BehavioralTrigger,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> dict:
    """Trigger a behavioral risk check."""
    behavioral_service = BehavioralService(db)
    await behavioral_service.trigger_check(trigger, current_user.id)
    return {"message": "Check triggered successfully"}
