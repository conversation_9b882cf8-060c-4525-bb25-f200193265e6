"""Dependencies for FastAPI routes."""

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON>asswordBearer
from jose import JWTError, jwt
from datetime import datetime, timedelta
from typing import Optional, Union, AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
import structlog

from core.config import settings
from db.session import get_async_session
from db.models import User, Device
from schemas.token import TokenData

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login", auto_error=False)

logger = structlog.get_logger(__name__)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    return jwt.encode(
        to_encode,
        settings.JWT_SECRET_KEY,
        algorithm=settings.JWT_ALGORITHM
    )

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: AsyncSession = Depends(get_async_session)
) -> User:
    """Get current authenticated user from JWT token."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        if not token:
            raise credentials_exception

        # For development: Accept test token
        if token == "test-token-for-behavioral-testing" and settings.DEBUG:
            # Return a mock user for testing
            from db.models import User
            mock_user = User(
                id=1,
                email="<EMAIL>",
                username="testuser",
                first_name="Test",
                last_name="User",
                password_hash="mock_hash",
                is_active=True,
                is_admin=False
            )
            return mock_user

        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
        token_data = TokenData(email=email)
    except JWTError:
        raise credentials_exception

    result = await db.execute(
        select(User).where(User.email == token_data.email)
    )
    user = result.scalar_one_or_none()

    if user is None:
        raise credentials_exception
    return user

async def get_current_user_id(
    current_user: User = Depends(get_current_user)
) -> int:
    """Get the ID of the current authenticated user."""
    return current_user.id

async def get_optional_user(
    token: Optional[str] = Depends(oauth2_scheme),
    db: AsyncSession = Depends(get_async_session)
) -> Optional[User]:
    """Get current user if authenticated, else None."""
    if not token:
        return None
        
    try:
        return await get_current_user(token, db)
    except HTTPException:
        return None

async def get_current_device(
    device_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> Device:
    """Get current device if it belongs to user."""
    device = await db.execute(
        select(Device).where(
            Device.id == device_id,
            Device.user_id == current_user.id
        )
    )
    device = device.scalar_one_or_none()
    
    if not device:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Device not found"
        )
        
    return device
