"""Database initialization script."""

import asyncio
from sqlalchemy.ext.asyncio import AsyncEngine
from db.session import engine
from db.models import Base

async def init_db(engine: AsyncEngine) -> None:
    """Initialize database tables."""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

async def main() -> None:
    """Main function to initialize database."""
    try:
        await init_db(engine)
        print("Database initialized successfully!")
    except Exception as e:
        print(f"Error initializing database: {e}")
    finally:
        await engine.dispose()

if __name__ == "__main__":
    asyncio.run(main())
