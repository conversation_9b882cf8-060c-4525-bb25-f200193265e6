"""Banking API endpoints."""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional

from core.deps import get_current_user
from core.logging_config import logger
from db.models import User as UserModel
from db.session import get_async_session
from services.banking import BankingService
from schemas.banking import (
    AccountCreate, AccountUpdate, AccountResponse, AccountSummary,
    TransactionCreate, TransactionResponse, TransactionHistory,
    TransferCreate, TransferResponse,
    PaymentCreate, PaymentResponse,
    BalanceResponse, AccountSummaryResponse, DashboardSummary
)

router = APIRouter(prefix="/api/v1/banking", tags=["banking"])


# ============================================================================
# ACCOUNT ENDPOINTS
# ============================================================================

@router.post("/accounts", status_code=status.HTTP_201_CREATED, response_model=AccountResponse)
async def create_account(
    account_data: AccountCreate,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> AccountResponse:
    """Create a new bank account."""
    try:
        banking_service = BankingService(db)
        return await banking_service.create_account(current_user.id, account_data)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating account: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create account")


@router.get("/accounts", response_model=List[AccountSummary])
async def get_accounts(
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> List[AccountSummary]:
    """Get all accounts for the current user."""
    try:
        banking_service = BankingService(db)
        return await banking_service.get_user_accounts(current_user.id)
    except Exception as e:
        logger.error(f"Error getting accounts: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get accounts")


@router.get("/accounts/{account_id}", response_model=AccountResponse)
async def get_account(
    account_id: int,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> AccountResponse:
    """Get a specific account."""
    try:
        banking_service = BankingService(db)
        account = await banking_service.get_account(current_user.id, account_id)
        if not account:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Account not found")
        return account
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting account {account_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get account")


@router.put("/accounts/{account_id}", response_model=AccountResponse)
async def update_account(
    account_id: int,
    account_data: AccountUpdate,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> AccountResponse:
    """Update account information."""
    try:
        banking_service = BankingService(db)
        account = await banking_service.update_account(current_user.id, account_id, account_data)
        if not account:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Account not found")
        return account
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating account {account_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update account")


# ============================================================================
# BALANCE ENDPOINTS
# ============================================================================

@router.get("/accounts/{account_id}/balance", response_model=BalanceResponse)
async def get_account_balance(
    account_id: int,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> BalanceResponse:
    """Get account balance information."""
    try:
        banking_service = BankingService(db)
        balance = await banking_service.get_account_balance(current_user.id, account_id)
        if not balance:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Account not found")
        return balance
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting balance for account {account_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get balance")


# ============================================================================
# TRANSACTION ENDPOINTS
# ============================================================================

@router.post("/transactions", status_code=status.HTTP_201_CREATED, response_model=TransactionResponse)
async def create_transaction(
    transaction_data: TransactionCreate,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> TransactionResponse:
    """Create a new transaction."""
    try:
        banking_service = BankingService(db)
        return await banking_service.create_transaction(current_user.id, transaction_data)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating transaction: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create transaction")


@router.get("/transactions", response_model=TransactionHistory)
async def get_transaction_history(
    account_id: Optional[int] = Query(None, description="Filter by account ID"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> TransactionHistory:
    """Get transaction history."""
    try:
        banking_service = BankingService(db)
        return await banking_service.get_transaction_history(current_user.id, account_id, page, page_size)
    except Exception as e:
        logger.error(f"Error getting transaction history: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get transactions")


# ============================================================================
# TRANSFER ENDPOINTS
# ============================================================================

@router.post("/transfers", status_code=status.HTTP_201_CREATED, response_model=TransferResponse)
async def create_transfer(
    transfer_data: TransferCreate,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> TransferResponse:
    """Create a money transfer between accounts."""
    try:
        banking_service = BankingService(db)
        return await banking_service.create_transfer(current_user.id, transfer_data)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating transfer: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create transfer")


# ============================================================================
# PAYMENT ENDPOINTS
# ============================================================================

@router.post("/payments", status_code=status.HTTP_201_CREATED, response_model=PaymentResponse)
async def create_payment(
    payment_data: PaymentCreate,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> PaymentResponse:
    """Create a payment to external payee."""
    try:
        banking_service = BankingService(db)
        return await banking_service.create_payment(current_user.id, payment_data)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating payment: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create payment")


# ============================================================================
# SUMMARY AND DASHBOARD ENDPOINTS
# ============================================================================

@router.get("/summary", response_model=AccountSummaryResponse)
async def get_account_summary(
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> AccountSummaryResponse:
    """Get account summary for dashboard."""
    try:
        banking_service = BankingService(db)
        return await banking_service.get_account_summary(current_user.id)
    except Exception as e:
        logger.error(f"Error getting account summary: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get summary")


@router.get("/dashboard", response_model=DashboardSummary)
async def get_dashboard_summary(
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> DashboardSummary:
    """Get comprehensive dashboard summary."""
    try:
        banking_service = BankingService(db)
        return await banking_service.get_dashboard_summary(current_user.id)
    except Exception as e:
        logger.error(f"Error getting dashboard summary: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get dashboard")


# ============================================================================
# DEMO DATA ENDPOINT
# ============================================================================

@router.post("/demo-data", status_code=status.HTTP_201_CREATED)
async def create_demo_data(
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> dict:
    """Create demo accounts and transactions for testing."""
    try:
        banking_service = BankingService(db)
        success = await banking_service.create_demo_data(current_user.id)
        if success:
            return {"message": "Demo data created successfully"}
        else:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create demo data")
    except Exception as e:
        logger.error(f"Error creating demo data: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create demo data")


# Legacy endpoints for backward compatibility
@router.get("/balance", response_model=dict)
async def get_balance_legacy(
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> dict:
    """Legacy endpoint for getting balance."""
    try:
        banking_service = BankingService(db)
        accounts = await banking_service.get_user_accounts(current_user.id)
        if not accounts:
            return {"balance": 0.0, "currency": "USD"}
        
        primary_account = next((acc for acc in accounts if acc.is_primary), accounts[0])
        return {
            "balance": float(primary_account.balance),
            "currency": primary_account.currency,
            "account_id": primary_account.id,
            "account_name": primary_account.account_name
        }
    except Exception as e:
        logger.error(f"Error getting legacy balance: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get balance")
