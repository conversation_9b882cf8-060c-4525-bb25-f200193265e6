# TrustChain-Auth ML Service Configuration
# Copy this file to .env and update values for your environment

# Service Configuration
SERVICE_NAME=trustchain-ml-service
SERVICE_VERSION=1.0.0
HOST=0.0.0.0
PORT=8000

# Model Configuration
MODEL_PATH=/app/models
AUTOENCODER_MODEL=behavioral_autoencoder.h5
SVM_MODEL=behavioral_svm.pkl
CONTRASTIVE_MODEL=contrastive_model.h5

# Processing Configuration
BATCH_SIZE=32
MAX_SEQUENCE_LENGTH=100
FEATURE_DIMENSION=128

# Performance Configuration
WORKERS=4
MAX_REQUESTS_PER_WORKER=1000
TIMEOUT_SECONDS=30

# Monitoring Configuration
METRICS_ENABLED=true
PROMETHEUS_PORT=9090

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Security Configuration
API_KEY_REQUIRED=false
RATE_LIMITING_ENABLED=true
MAX_REQUESTS_PER_MINUTE=100

# Development/Production Mode
ENVIRONMENT=development
DEBUG=false
