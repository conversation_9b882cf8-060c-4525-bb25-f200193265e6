#!/usr/bin/env python3
"""
Script to fix Flutter Color.withValues() method calls to use withOpacity() instead.
This fixes compatibility issues with newer Flutter versions.
"""

import os
import re
import glob

def fix_color_methods(file_path):
    """Fix withValues(alpha: x) to withOpacity(x) in a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Pattern to match .withValues(alpha: x) and replace with .withOpacity(x)
        pattern = r'\.withValues\(alpha:\s*([0-9.]+)\)'
        replacement = r'.withOpacity(\1)'
        
        new_content = re.sub(pattern, replacement, content)
        
        # Also fix .toARGB32() method calls (replace with .value)
        pattern2 = r'\.toARGB32\(\)'
        replacement2 = r'.value'
        
        new_content = re.sub(pattern2, replacement2, new_content)
        
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"Fixed: {file_path}")
            return True
        return False
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Main function to process all Dart files."""
    # Find all .dart files in the mobile_app directory
    dart_files = glob.glob('mobile_app/**/*.dart', recursive=True)
    
    fixed_count = 0
    for file_path in dart_files:
        if fix_color_methods(file_path):
            fixed_count += 1
    
    print(f"\nFixed {fixed_count} files out of {len(dart_files)} total Dart files.")

if __name__ == '__main__':
    main()
