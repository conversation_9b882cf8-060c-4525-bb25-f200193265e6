"""Tests for behavioral autoencoder model."""
import pytest
import numpy as np
import tensorflow as tf
from pathlib import Path
import tempfile
import json

from models.autoencoder import BehavioralAutoencoder
from data_collection.preprocessor import BehavioralFeatureExtractor

@pytest.fixture
def sample_data():
    """Create sample behavioral data for testing."""
    np.random.seed(42)
    n_samples = 100
    n_features = 20
    
    # Generate normal behavioral data
    normal_data = np.random.normal(0, 1, (n_samples, n_features))
    
    # Generate anomalous data with different distribution
    anomalous_data = np.random.normal(3, 2, (n_samples // 10, n_features))
    
    return normal_data, anomalous_data

@pytest.fixture
def model():
    """Create test autoencoder model."""
    return BehavioralAutoencoder(
        input_dim=20,
        encoding_dim=10,
        hidden_layers=[16, 12]
    )

def test_model_creation(model):
    """Test model architecture and parameters."""
    assert isinstance(model, BehavioralAutoencoder)
    assert model.input_dim == 20
    assert model.encoding_dim == 10
    assert len(model.encoder.layers) > 0
    assert len(model.decoder.layers) > 0

def test_model_training(model, sample_data):
    """Test model training process."""
    normal_data, _ = sample_data
    
    # Train model
    history = model.fit(
        normal_data,
        epochs=5,
        batch_size=32,
        validation_split=0.2
    )
    
    assert 'loss' in history.history
    assert 'val_loss' in history.history
    assert len(history.history['loss']) == 5

def test_model_prediction(model, sample_data):
    """Test model predictions and anomaly detection."""
    normal_data, anomalous_data = sample_data
    
    # Train model
    model.fit(normal_data, epochs=5, batch_size=32)
    
    # Get predictions
    normal_scores = model.predict(normal_data)
    anomaly_scores = model.predict(anomalous_data)
    
    assert normal_scores.shape == (len(normal_data),)
    assert anomaly_scores.shape == (len(anomalous_data),)
    
    # Anomalous data should have higher reconstruction error
    assert np.mean(anomaly_scores) > np.mean(normal_scores)

def test_model_save_load(model, sample_data, tmp_path):
    """Test model serialization."""
    normal_data, _ = sample_data
    
    # Train model
    model.fit(normal_data, epochs=2, batch_size=32)
    
    # Save model
    save_path = tmp_path / "test_model"
    model.save(save_path)
    
    # Load model
    loaded_model = BehavioralAutoencoder.load(save_path)
    
    # Compare predictions
    original_pred = model.predict(normal_data)
    loaded_pred = loaded_model.predict(normal_data)
    
    np.testing.assert_array_almost_equal(original_pred, loaded_pred)

def test_preprocessing_integration(sample_data):
    """Test integration with feature preprocessor."""
    preprocessor = BehavioralFeatureExtractor()
    normal_data, _ = sample_data
    
    # Create sample raw data
    raw_data = {
        "typing_data": [
            {"key": "a", "press_time": 100, "release_time": 150},
            {"key": "b", "press_time": 200, "release_time": 250}
        ],
        "touch_data": [
            {"x": 100, "y": 200, "pressure": 0.5, "time": 300},
            {"x": 150, "y": 250, "pressure": 0.7, "time": 400}
        ]
    }
    
    # Extract features
    features = preprocessor.extract_features(raw_data)
    
    assert isinstance(features, np.ndarray)
    assert len(features.shape) == 1