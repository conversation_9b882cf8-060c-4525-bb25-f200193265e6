"""TrustChain-Auth API v1 Package."""

from fastapi import APIRouter
from .auth import router as auth_router
from .users import router as users_router
from .behavioral import router as behavioral_router
from .events import router as events_router
from .devices import router as devices_router
from .banking import router as banking_router

router = APIRouter()

router.include_router(auth_router)
router.include_router(users_router)
router.include_router(behavioral_router)
router.include_router(events_router)
router.include_router(devices_router)
router.include_router(banking_router)
