{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "battery_plus", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\battery_plus-4.1.0\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\device_info_plus-9.1.2\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage-9.2.4\\\\", "native_build": true, "dependencies": []}, {"name": "integration_test", "path": "C:\\\\Users\\\\<USER>\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": []}, {"name": "local_auth_darwin", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\local_auth_darwin-1.4.3\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\permission_handler_apple-9.1.4\\\\", "native_build": true, "dependencies": []}, {"name": "sensors_plus", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\sensors_plus-3.1.0\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "tflite_flutter", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\tflite_flutter-0.11.0\\\\", "native_build": true, "dependencies": []}], "android": [{"name": "battery_plus", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\battery_plus-4.1.0\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\device_info_plus-9.1.2\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\flutter_plugin_android_lifecycle-2.0.26\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage-9.2.4\\\\", "native_build": true, "dependencies": []}, {"name": "integration_test", "path": "C:\\\\Users\\\\<USER>\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": []}, {"name": "local_auth_android", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\local_auth_android-1.0.47\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "path_provider_android", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\path_provider_android-2.2.15\\\\", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\permission_handler_android-10.3.6\\\\", "native_build": true, "dependencies": []}, {"name": "sensors_plus", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\sensors_plus-3.1.0\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\shared_preferences_android-2.4.7\\\\", "native_build": true, "dependencies": []}, {"name": "tflite_flutter", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\tflite_flutter-0.11.0\\\\", "native_build": true, "dependencies": []}], "macos": [{"name": "battery_plus", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\battery_plus-4.1.0\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\device_info_plus-9.1.2\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage_macos", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_macos-3.1.3\\\\", "native_build": true, "dependencies": []}, {"name": "local_auth_darwin", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\local_auth_darwin-1.4.3\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "tflite_flutter", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\tflite_flutter-0.11.0\\\\", "native_build": true, "dependencies": []}], "linux": [{"name": "battery_plus", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\battery_plus-4.1.0\\\\", "native_build": false, "dependencies": []}, {"name": "device_info_plus", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\device_info_plus-9.1.2\\\\", "native_build": false, "dependencies": []}, {"name": "flutter_secure_storage_linux", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_linux-1.2.3\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_linux", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": []}, {"name": "shared_preferences_linux", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\shared_preferences_linux-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"]}, {"name": "tflite_flutter", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\tflite_flutter-0.11.0\\\\", "native_build": true, "dependencies": []}], "windows": [{"name": "battery_plus", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\battery_plus-4.1.0\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\device_info_plus-9.1.2\\\\", "native_build": false, "dependencies": []}, {"name": "flutter_secure_storage_windows", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_windows-3.1.2\\\\", "native_build": true, "dependencies": []}, {"name": "local_auth_windows", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\local_auth_windows-1.0.11\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_windows", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\permission_handler_windows-0.1.3\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_windows", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\shared_preferences_windows-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"]}, {"name": "tflite_flutter", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\tflite_flutter-0.11.0\\\\", "native_build": true, "dependencies": []}], "web": [{"name": "battery_plus", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\battery_plus-4.1.0\\\\", "dependencies": []}, {"name": "device_info_plus", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\device_info_plus-9.1.2\\\\", "dependencies": []}, {"name": "flutter_secure_storage_web", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\flutter_secure_storage_web-1.2.1\\\\", "dependencies": []}, {"name": "sensors_plus", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\sensors_plus-3.1.0\\\\", "dependencies": []}, {"name": "shared_preferences_web", "path": "D:\\\\pub-cache\\\\hosted\\\\pub.dev\\\\shared_preferences_web-2.4.3\\\\", "dependencies": []}]}, "dependencyGraph": [{"name": "battery_plus", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_macos", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": ["path_provider"]}, {"name": "integration_test", "dependencies": []}, {"name": "local_auth", "dependencies": ["local_auth_android", "local_auth_darwin", "local_auth_windows"]}, {"name": "local_auth_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "local_auth_darwin", "dependencies": []}, {"name": "local_auth_windows", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "sensors_plus", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "tflite_flutter", "dependencies": []}], "date_created": "2025-07-08 18:04:38.826590", "version": "3.24.5", "swift_package_manager_enabled": false}