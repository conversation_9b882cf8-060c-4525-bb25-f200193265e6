"""Event related routes."""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from datetime import datetime

from core.deps import get_current_user, get_optional_user
from core.logging_config import logger
from schemas.behavioral import BehavioralEvent, BehavioralEventCreate
from services.behavioral import BehavioralService
from db.models import User as UserModel
from db.session import get_async_session

router = APIRouter(prefix="/api/v1/events", tags=["events"])

@router.post("", status_code=status.HTTP_201_CREATED)
async def create_event(
    event_data: BehavioralEventCreate,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> dict:
    """Create a new behavioral event."""
    behavioral_service = BehavioralService(db)
    await behavioral_service.create_event(event_data, current_user.id)
    return {"message": "Event created successfully"}

@router.get("", response_model=List[BehavioralEvent])
async def get_events(
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> List[BehavioralEvent]:
    """Get user's behavioral events."""
    behavioral_service = BehavioralService(db)
    return await behavioral_service.get_user_events(current_user.id)

@router.get("/{event_id}", response_model=BehavioralEvent)
async def get_event(
    event_id: str,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> BehavioralEvent:
    """Get event by ID."""
    behavioral_service = BehavioralService(db)
    event = await behavioral_service.get_event(event_id, current_user.id)
    return event
