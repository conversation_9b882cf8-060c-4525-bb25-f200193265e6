"""Device related routes."""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from core.deps import get_current_user
from schemas.device import Device, DeviceCreate, DeviceUpdate
from services.auth import AuthService
from db.models import User as UserModel
from db.session import get_async_session

router = APIRouter(prefix="/api/v1/devices", tags=["devices"])

@router.post("", status_code=status.HTTP_201_CREATED, response_model=Device)
async def register_device(
    device_data: DeviceCreate,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> Device:
    """Register a new device."""
    auth_service = AuthService(db)
    device = await auth_service.register_device(device_data, current_user.id)
    return device

@router.get("", response_model=List[Device])
async def get_user_devices(
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> List[Device]:
    """Get user's registered devices."""
    auth_service = AuthService(db)
    return await auth_service.get_user_devices(current_user.id)

@router.get("/{device_id}", response_model=Device)
async def get_device(
    device_id: str,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> Device:
    """Get device by ID."""
    auth_service = AuthService(db)
    device = await auth_service.get_device(device_id, current_user.id)
    return device

@router.put("/{device_id}", response_model=Device)
async def update_device(
    device_id: str,
    device_data: DeviceUpdate,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> Device:
    """Update device."""
    auth_service = AuthService(db)
    device = await auth_service.update_device(device_id, device_data, current_user.id)
    return device

@router.delete("/{device_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_device(
    device_id: str,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> None:
    """Delete device."""
    auth_service = AuthService(db)
    await auth_service.delete_device(device_id, current_user.id)

@router.post("/{device_id}/revoke", status_code=status.HTTP_204_NO_CONTENT)
async def revoke_device(
    device_id: str,
    current_user: UserModel = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
) -> None:
    """Revoke device access."""
    auth_service = AuthService(db)
    await auth_service.revoke_device(device_id, current_user.id)
