"""User model."""

from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime

from db.base_class import Base

class User(Base):
    """User model for authentication and user data."""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    password_hash = Column(String)
    first_name = Column(String)
    last_name = Column(String)
    full_name = Column(String)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    devices = relationship("Device", back_populates="user")
    auth_events = relationship("AuthEvent", back_populates="user")
    security_alerts = relationship("SecurityAlert", back_populates="user")
    behavioral_events = relationship("BehavioralEvent", back_populates="user")
    behavioral_features = relationship("BehavioralFeature", back_populates="user")
    behavioral_profile = relationship("BehavioralProfile", back_populates="user", uselist=False)
    risk_assessments = relationship("RiskAssessment", back_populates="user")
