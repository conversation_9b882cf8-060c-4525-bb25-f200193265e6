Write-Host "Starting Docker cleanup..." -ForegroundColor Green

# Stop Docker Desktop if running
Write-Host "Stopping Docker Desktop if running..." -ForegroundColor Yellow
$dockerProcess = Get-Process "Docker Desktop" -ErrorAction SilentlyContinue
if ($dockerProcess) {
    $dockerProcess | Stop-Process -Force
    Start-Sleep -Seconds 5
}

# Stop WSL and force cleanup
Write-Host "Shutting down WSL..." -ForegroundColor Yellow
wsl --shutdown
Start-Sleep -Seconds 2

# Force cleanup of WSL and Docker WSL instances
Write-Host "Force cleaning WSL distributions..." -ForegroundColor Yellow
wsl --terminate docker-desktop
wsl --terminate docker-desktop-data
Start-Sleep -Seconds 2

Write-Host "Unregistering Docker WSL distributions..." -ForegroundColor Yellow
wsl --unregister docker-desktop
wsl --unregister docker-desktop-data

# Clean up both C: and D: WSL files to start fresh
Write-Host "Cleaning up WSL files from both C: and D: drives..." -ForegroundColor Yellow
$wslLocations = @(
    "C:\WSL",
    "D:\WSL",
    "$env:LOCALAPPDATA\Docker\wsl",
    "$env:LOCALAPPDATA\Packages\*DockerDesktop*",
    "D:\docker-data\wsl"
)

foreach ($loc in $wslLocations) {
    if (Test-Path $loc) {
        Write-Host "Removing WSL location: $loc" -ForegroundColor Yellow
        Remove-Item -Path $loc -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# Remove Docker data directories
$directories = @(
    "$env:LOCALAPPDATA\Docker",
    "$env:APPDATA\Docker",
    "C:\ProgramData\Docker",
    "C:\ProgramData\DockerDesktop",
    "$env:TEMP\docker*",
    "$env:USERPROFILE\.docker",
    "C:\Users\<USER>\Documents\Hyper-V\Virtual hard disks\*docker*"
)

foreach ($dir in $directories) {
    Get-ChildItem -Path $dir -Recurse -Force -ErrorAction SilentlyContinue | 
    Where-Object { $_.PSIsContainer -eq $false } | 
    ForEach-Object {
        Write-Host "Removing file: $($_.FullName)" -ForegroundColor Yellow
        Remove-Item -Path $_.FullName -Force -ErrorAction SilentlyContinue
    }
    
    if (Test-Path $dir) {
        Write-Host "Removing directory: $dir" -ForegroundColor Yellow
        Remove-Item -Path $dir -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# Clean Windows temp folders of docker-related files
Write-Host "Cleaning Windows temp folders..." -ForegroundColor Yellow
Get-ChildItem -Path $env:TEMP -Filter "docker*" -Recurse -Force -ErrorAction SilentlyContinue | 
ForEach-Object {
    Write-Host "Removing temp file/folder: $($_.FullName)" -ForegroundColor Yellow
    Remove-Item -Path $_.FullName -Recurse -Force -ErrorAction SilentlyContinue
}

# Create new Docker data directory on D drive
$newDockerPath = "D:\docker-data"
if (-not (Test-Path $newDockerPath)) {
    Write-Host "Creating new Docker data directory at $newDockerPath" -ForegroundColor Yellow
    New-Item -Path $newDockerPath -ItemType Directory -Force
}

# Create project-specific directories
$projectDirs = @(
    "D:\docker-data\trustchain\postgres",
    "D:\docker-data\trustchain\redis",
    "D:\docker-data\trustchain\prometheus"
)
foreach ($dir in $projectDirs) {
    if (-not (Test-Path $dir)) {
        Write-Host "Creating project directory: $dir" -ForegroundColor Yellow
        New-Item -Path $dir -ItemType Directory -Force
    }
}

Write-Host "`nCleanup completed!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Start Docker Desktop" -ForegroundColor Cyan
Write-Host "2. Go to Docker Desktop Settings -> Resources -> Advanced" -ForegroundColor Cyan
Write-Host "3. Change 'Disk image location' to 'D:\docker-data'" -ForegroundColor Cyan
Write-Host "4. Click 'Apply & Restart'" -ForegroundColor Cyan
