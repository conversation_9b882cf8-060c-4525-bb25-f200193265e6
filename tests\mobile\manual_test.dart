import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:trustchain_auth/main.dart';
import 'test_helper.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets('Manual Testing Mode', (WidgetTester tester) async {
    try {
      // Initialize TestHelper and logging
      TestHelper.initializeLogging();
      TestHelper.printManualTestInstructions();

      // Log startup
      TestHelper.logSystemEvent('Application starting in manual test mode');
      final deviceInfo = TestHelper.getDeviceInfo();
      TestHelper.logSystemEvent('Device info initialized');
      
      // Launch the app
      await tester.pumpWidget(await createApp());
      await tester.pumpAndSettle();

      TestHelper.logSystemEvent('Application ready for manual testing');
      TestHelper.logSystemEvent('Environment: ${TestHelper.getCurrentEnvironment()}');
      
      // Keep the app running indefinitely for manual testing
      // This will be interrupted by Ctrl+C
      while (true) {
        try {
          await tester.pump(const Duration(milliseconds: 100));
        } catch (e) {
          // Ignore errors during pump to keep the app running
          TestHelper.logSystemEvent('Frame update error: $e');
        }
        await Future.delayed(const Duration(milliseconds: 50));
      }
    } catch (e, stackTrace) {
      TestHelper.logError('Manual test mode error', e, stackTrace);
      rethrow;
    }
  });
}
